# Cloudinary Integration Guide - LGU Project

## Overview

This document provides a comprehensive guide for the Cloudinary integration in the LGU Project. The integration enables professional media management with automatic optimization, transformations, and a robust upload system.

## Configuration

### Environment Variables

Add the following variables to your `.env.local` file:

```env
# Cloudinary Configuration for LGU Project
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=dvwaviwn0
CLOUDINARY_API_KEY=382949993714981
CLOUDINARY_API_SECRET=CJtDZ_9BnD74NgOuTpuOI9ljhI8
```

### Upload Preset Configuration

The project uses the following upload preset configured in Cloudinary:

- **Name**: `lgu_project`
- **Mode**: Unsigned
- **Settings**:
  - Overwrite: false
  - Use filename: false
  - Unique filename: true
  - Use filename as display name: true
  - Asset folder: `lgu-uploads`

## Dependencies

The integration uses the following packages:

```json
{
  "next-cloudinary": "^6.x.x",
  "cloudinary": "^2.x.x"
}
```

Install with:
```bash
npm install next-cloudinary cloudinary
```

## Core Components

### 1. CloudinaryUploadWidget

A reusable upload component that wraps the Cloudinary Upload Widget.

**Location**: `src/components/CloudinaryUploadWidget.tsx`

**Features**:
- Drag and drop upload
- Multiple file selection
- Image preview and cropping
- Progress tracking
- Error handling
- Customizable upload presets

**Usage**:
```tsx
import CloudinaryUploadWidget from '@/components/CloudinaryUploadWidget'

<CloudinaryUploadWidget
  onUploadSuccess={(result) => console.log('Uploaded:', result)}
  folder="personnel"
  multiple={false}
  cropping={true}
  buttonText="Upload Photo"
/>
```

### 2. CloudinaryImage

An optimized image display component using Cloudinary transformations.

**Location**: `src/components/CloudinaryImage.tsx`

**Features**:
- Automatic optimization (format, quality)
- Responsive sizing
- Lazy loading
- Error handling with fallback
- Various preset configurations

**Usage**:
```tsx
import { CloudinaryImagePresets } from '@/components/CloudinaryImage'

<CloudinaryImagePresets.Profile
  src="personnel/john-doe"
  alt="John Doe Profile"
  size={200}
/>
```

### 3. Cloudinary Utilities

Core utility functions for Cloudinary operations.

**Location**: `src/lib/cloudinary.ts`

**Features**:
- Upload functions
- URL generation
- Image transformations
- Configuration management

## Folder Structure

The project organizes media in the following folder structure:

```
lgu-uploads/
├── personnel/          # Personnel profile photos
├── documents/          # Document uploads
├── media/             # General media files
└── temp/              # Temporary uploads
```

## API Routes

### Upload API

**Endpoint**: `/api/cloudinary/upload`

**Method**: POST

**Content-Type**: multipart/form-data

**Parameters**:
- `file`: File to upload
- `folder`: Target folder (optional)
- `tags`: Array of tags (optional)
- `personnel_id`: Personnel ID for database linking (optional)
- `document_type`: Document type (optional)

**Example**:
```javascript
const formData = new FormData()
formData.append('file', file)
formData.append('folder', 'lgu-uploads/personnel')
formData.append('personnel_id', '123')

const response = await fetch('/api/cloudinary/upload', {
  method: 'POST',
  body: formData
})
```

## Integration Points

### 1. Media Center Page

**Location**: `src/app/admin/media/page.tsx`

**Features**:
- Real-time media statistics
- Upload functionality
- Media grid display
- Search and filtering

### 2. Personnel Management

**Location**: `src/components/PersonnelModal.tsx`

**Features**:
- Profile photo upload
- Automatic cropping to square aspect ratio
- Integration with personnel database

### 3. Document Management

**Location**: `src/app/admin/documents/page.tsx`

**Features**:
- Document upload
- File type validation
- Metadata storage

## Testing

### Test Page

A dedicated test page is available at `/test-cloudinary` for verifying the integration.

**Features**:
- Upload testing
- Configuration display
- Success/error handling
- Direct links to Cloudinary console

### Manual Testing Steps

1. **Navigate to Test Page**:
   ```
   http://localhost:3001/test-cloudinary
   ```

2. **Upload Test Images**:
   - Click "Test Upload to Cloudinary"
   - Select one or more images
   - Verify upload success

3. **Verify in Cloudinary**:
   - Check Cloudinary Media Library
   - Confirm images appear in `lgu-uploads/test` folder
   - Verify transformations work

4. **Test Personnel Photos**:
   - Go to Personnel page
   - Add/Edit personnel
   - Upload profile photo
   - Verify cropping and display

## Troubleshooting

### Common Issues

1. **Upload Widget Not Loading**:
   - Check environment variables
   - Verify cloud name is correct
   - Check browser console for errors

2. **Upload Fails**:
   - Verify upload preset exists
   - Check file size limits (10MB default)
   - Verify file type is allowed

3. **Images Not Displaying**:
   - Check public_id format
   - Verify image exists in Cloudinary
   - Check transformation parameters

### Debug Mode

Enable debug logging by adding to your component:

```tsx
console.log('[Cloudinary] Debug info:', {
  cloudName: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  uploadPreset: 'lgu_project',
  folder: 'lgu-uploads/test'
})
```

## Security Considerations

1. **Environment Variables**:
   - Never expose API secret in client-side code
   - Use unsigned uploads for client-side operations
   - Implement server-side validation

2. **Upload Restrictions**:
   - File type validation
   - File size limits
   - Folder restrictions
   - User authentication

3. **Access Control**:
   - Implement proper user permissions
   - Validate upload sources
   - Monitor usage and costs

## Performance Optimization

1. **Image Optimization**:
   - Use automatic format selection (`f_auto`)
   - Enable quality optimization (`q_auto`)
   - Implement responsive images

2. **Lazy Loading**:
   - Use Next.js Image component features
   - Implement intersection observer
   - Progressive image loading

3. **Caching**:
   - Leverage Cloudinary CDN
   - Implement browser caching
   - Use appropriate cache headers

## Monitoring and Analytics

1. **Cloudinary Dashboard**:
   - Monitor usage statistics
   - Track transformation requests
   - Review bandwidth usage

2. **Application Logging**:
   - Log upload success/failures
   - Track user interactions
   - Monitor error rates

## Future Enhancements

1. **Advanced Features**:
   - Video upload support
   - AI-powered auto-tagging
   - Background removal
   - Advanced transformations

2. **Integration Improvements**:
   - Bulk upload functionality
   - Advanced search capabilities
   - Media library management
   - Automated backups

## Support and Resources

- [Cloudinary Documentation](https://cloudinary.com/documentation)
- [Next Cloudinary Docs](https://next-cloudinary.spacejelly.dev/)
- [Upload Widget Reference](https://cloudinary.com/documentation/upload_widget_reference)
- [Transformation Reference](https://cloudinary.com/documentation/transformation_reference)

## Conclusion

The Cloudinary integration provides a robust, scalable solution for media management in the LGU Project. With automatic optimization, powerful transformations, and seamless Next.js integration, it ensures optimal performance and user experience.

For any issues or questions, refer to the troubleshooting section or consult the official Cloudinary documentation.
