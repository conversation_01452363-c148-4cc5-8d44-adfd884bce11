{"name": "lgu-project-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "fix-tailwind": "node scripts/fix-tailwind.js", "clean": "rmdir /s /q .next 2>nul & rmdir /s /q node_modules\\.cache 2>nul & del tsconfig.tsbuildinfo 2>nul & echo Clean completed", "clean:unix": "rm -rf .next && rm -rf node_modules/.cache && rm -f tsconfig.tsbuildinfo", "reset": "npm run clean && npm install && npm run dev"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^5.1.1", "@reduxjs/toolkit": "^2.8.2", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "autoprefixer": "^10.4.21", "cloudinary": "^2.6.1", "dotenv": "^16.5.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "lucide-react": "^0.513.0", "next": "15.3.3", "next-cloudinary": "^6.16.0", "postcss": "^8.5.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.57.0", "react-redux": "^9.2.0", "resend": "^4.5.2", "tailwindcss": "^3.4.17", "zod": "^3.25.56"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/echarts": "^4.9.22", "@types/node": "^20", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "eslint": "^9", "eslint-config-next": "15.3.3", "typescript": "^5"}}