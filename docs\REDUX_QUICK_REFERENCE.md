# Redux Toolkit Quick Reference

## 🚀 **Import Hooks**

```typescript
import { useAuth, usePersonnel, useUsers, useUI, useSettings } from '@/lib/redux/hooks'
```

---

## 🔐 **Authentication**

```typescript
const { 
  user, 
  isAuthenticated, 
  loading, 
  signIn, 
  signOut, 
  signUp 
} = useAuth()

// Sign in
await signIn('<EMAIL>', 'password')

// Sign out
await signOut()

// Sign up
await signUp('<EMAIL>', 'password', { name: '<PERSON>' })
```

---

## 👥 **Personnel Management**

```typescript
const {
  personnel,
  selectedPersonnel,
  pagination,
  filters,
  viewMode,
  loading,
  fetchPersonnelList,
  createNewPersonnel,
  updatePersonnelData,
  deletePersonnelData,
  selectPersonnel,
  updateFilters,
  updateViewMode
} = usePersonnel()

// Fetch personnel
fetchPersonnelList({ page: 1, limit: 10, search: 'john' })

// Create personnel
await createNewPersonnel({
  name: '<PERSON>',
  email: '<EMAIL>',
  department: 'Fisheries'
})

// Update personnel
await updatePersonnelData({ id: 1, name: '<PERSON>' })

// Delete personnel
await deletePersonnelData(1)

// Set filters
updateFilters({ search: 'john', sortBy: 'name_asc' })

// Change view mode
updateViewMode('cards') // or 'list'
```

---

## 👤 **User Management**

```typescript
const {
  users,
  selectedUser,
  loading,
  fetchUsersList,
  createNewUser,
  updateUserData,
  deleteUserData
} = useUsers()

// Fetch users
fetchUsersList({ page: 1, limit: 10 })

// Create user
await createNewUser({
  name: 'Admin User',
  email: '<EMAIL>',
  role: 'Admin'
})
```

---

## 🎨 **UI Management**

```typescript
const {
  showModal,
  hideModal,
  showNotification,
  hideNotification,
  theme,
  changeTheme,
  setSidebar,
  toggleSidebarState,
  updatePageTitle,
  updateBreadcrumbs
} = useUI()

// Show modal
showModal({
  id: 'personnel-modal',
  type: 'personnel',
  data: { mode: 'create' }
})

// Hide modal
hideModal('personnel-modal')

// Show notification
showNotification({
  type: 'success', // 'success' | 'error' | 'warning' | 'info'
  title: 'Success',
  message: 'Operation completed successfully'
})

// Change theme
changeTheme('dark') // 'light' | 'dark' | 'system'

// Update page title
updatePageTitle('Personnel Management')

// Update breadcrumbs
updateBreadcrumbs([
  { label: 'Admin', href: '/admin' },
  { label: 'Personnel', href: '/admin/personnel' }
])
```

---

## ⚙️ **Settings Management**

```typescript
const {
  userPreferences,
  systemSettings,
  featureFlags,
  savePreferences,
  toggleFlag
} = useSettings()

// Save user preferences
await savePreferences({
  language: 'en',
  itemsPerPage: 25,
  defaultView: 'cards'
})

// Toggle feature flag
toggleFlag('enableChatbot')

// Check feature flag
const chatbotEnabled = featureFlags.enableChatbot
```

---

## 🔄 **Common Patterns**

### **Loading States**
```typescript
const { loading, operationLoading } = usePersonnel()

if (loading) {
  return <LoadingSpinner />
}
```

### **Error Handling**
```typescript
const { error, clearError } = usePersonnel()

useEffect(() => {
  if (error) {
    showNotification({
      type: 'error',
      title: 'Error',
      message: error
    })
    clearError()
  }
}, [error])
```

### **Form Submission**
```typescript
const handleSubmit = async (formData) => {
  try {
    await createNewPersonnel(formData)
    showNotification({
      type: 'success',
      title: 'Success',
      message: 'Personnel created successfully'
    })
    hideModal('personnel-modal')
  } catch (error) {
    showNotification({
      type: 'error',
      title: 'Error',
      message: 'Failed to create personnel'
    })
  }
}
```

### **Pagination**
```typescript
const { pagination, updatePagination } = usePersonnel()

const handlePageChange = (newPage) => {
  updatePagination({ page: newPage })
}
```

### **Search & Filters**
```typescript
const { filters, updateFilters } = usePersonnel()

const handleSearch = (searchTerm) => {
  updateFilters({ search: searchTerm })
}

const handleSortChange = (sortBy) => {
  updateFilters({ sortBy })
}
```

---

## 🎯 **Quick Actions**

| Action | Code |
|--------|------|
| Show success message | `showNotification({ type: 'success', title: 'Success', message: 'Done!' })` |
| Show error message | `showNotification({ type: 'error', title: 'Error', message: 'Failed!' })` |
| Open modal | `showModal({ id: 'modal-id', type: 'custom', data: {} })` |
| Close modal | `hideModal('modal-id')` |
| Toggle theme | `changeTheme(theme === 'light' ? 'dark' : 'light')` |
| Update page title | `updatePageTitle('New Page Title')` |
| Toggle sidebar | `toggleSidebarState()` |
| Fetch data | `fetchPersonnelList({ page: 1, limit: 10 })` |

---

## 🧪 **Testing**

Visit `/test-redux` to test all Redux functionality.

---

## 🔧 **TypeScript**

All hooks are fully typed. Use TypeScript for better development experience:

```typescript
import type { Personnel } from '@/lib/redux/slices/personnelSlice'
import type { RootState } from '@/lib/store'
```

---

## 📱 **Mobile Responsive**

All Redux state works seamlessly with responsive design:

```typescript
const { sidebarOpen, setSidebar } = useUI()

// Auto-close sidebar on mobile
useEffect(() => {
  if (window.innerWidth < 768) {
    setSidebar(false)
  }
}, [])
```

---

## 🎉 **You're Ready!**

Redux Toolkit is fully integrated and ready to use. Start migrating your components and enjoy the improved state management!
