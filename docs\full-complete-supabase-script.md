
-- =====================================================
-- LGU Project App - Complete Supabase Database Schema
-- =====================================================
--
-- UPDATED VERSION - PRODUCTION READY
-- Includes all tables, functions, and policies for:
-- - User management
-- - Personnel management
-- - Document management
-- - Enterprise media library with bidirectional Cloudinary sync
-- - Complete audit trails and security policies
--
-- COPY AND PASTE THIS ENTIRE SCRIPT INTO SUPABASE SQL EDITOR
--
-- Last Updated: January 2025
-- Version: 2.0 (Media Library Enhanced)
-- =====================================================

-- Create custom types/enums (with IF NOT EXISTS handling)
DO $$ BEGIN
    CREATE TYPE user_status AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE personnel_status AS ENUM ('Active', 'Inactive', 'On Leave', 'Suspended');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE media_sync_status AS ENUM ('synced', 'pending', 'error');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE media_sync_operation AS ENUM ('upload', 'delete', 'update', 'restore');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- =====================================================
-- TABLES
-- =====================================================

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id BIGSERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    role VARCHAR(50) DEFAULT 'user',
    status user_status DEFAULT 'ACTIVE',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Personnel table
CREATE TABLE IF NOT EXISTS personnel (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    profile_photo VARCHAR(500),
    department VARCHAR(255) NOT NULL,
    position VARCHAR(255),
    hire_date DATE,
    status personnel_status DEFAULT 'Active',
    biography TEXT,
    spouse_name VARCHAR(255),
    spouse_occupation VARCHAR(255),
    children_count VARCHAR(10),
    emergency_contact VARCHAR(50),
    children_names TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Personnel Documents table
CREATE TABLE IF NOT EXISTS personnel_documents (
    id BIGSERIAL PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    size INTEGER NOT NULL,
    path VARCHAR(500) NOT NULL,
    personnel_id BIGINT NOT NULL REFERENCES personnel(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ENTERPRISE MEDIA LIBRARY TABLES
-- =====================================================

-- Media Assets table - Core table for bidirectional Cloudinary sync
CREATE TABLE IF NOT EXISTS media_assets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Cloudinary identifiers (required for sync)
    cloudinary_public_id VARCHAR(500) NOT NULL UNIQUE,
    cloudinary_version INTEGER NOT NULL DEFAULT 1,
    cloudinary_signature VARCHAR(255) NOT NULL,
    cloudinary_etag VARCHAR(255),

    -- File information
    original_filename VARCHAR(500),
    display_name VARCHAR(500),
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    format VARCHAR(50) NOT NULL,
    width INTEGER,
    height INTEGER,
    duration DECIMAL(10,2), -- For video files

    -- Organization and categorization
    folder VARCHAR(500),
    tags TEXT[] DEFAULT '{}',
    description TEXT,
    alt_text VARCHAR(500), -- For accessibility

    -- URLs (cached for performance)
    secure_url VARCHAR(1000) NOT NULL,
    url VARCHAR(1000) NOT NULL,
    thumbnail_url VARCHAR(1000),

    -- Resource type and access
    resource_type VARCHAR(50) NOT NULL DEFAULT 'image', -- image, video, raw
    access_mode VARCHAR(50) DEFAULT 'public', -- public, authenticated

    -- User and business context
    uploaded_by UUID, -- References auth.users(id) or users table
    used_in_personnel BIGINT REFERENCES personnel(id),
    used_in_documents BIGINT REFERENCES personnel_documents(id),

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    cloudinary_created_at TIMESTAMP WITH TIME ZONE,

    -- Sync management
    sync_status media_sync_status DEFAULT 'synced',
    last_synced_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sync_error_message TEXT,
    sync_retry_count INTEGER DEFAULT 0,

    -- Soft delete for data integrity
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID, -- References auth.users(id) or users table

    -- Constraints
    CONSTRAINT valid_resource_type CHECK (resource_type IN ('image', 'video', 'raw')),
    CONSTRAINT valid_access_mode CHECK (access_mode IN ('public', 'authenticated')),
    CONSTRAINT positive_file_size CHECK (file_size > 0),
    CONSTRAINT positive_dimensions CHECK (
        (width IS NULL OR width > 0) AND
        (height IS NULL OR height > 0)
    )
);

-- Media Sync Log table - Audit trail for all sync operations
CREATE TABLE IF NOT EXISTS media_sync_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Operation details
    operation media_sync_operation NOT NULL,
    status media_sync_status NOT NULL,

    -- Asset reference
    media_asset_id UUID REFERENCES media_assets(id) ON DELETE SET NULL,
    cloudinary_public_id VARCHAR(500) NOT NULL,

    -- Sync details
    source VARCHAR(50) NOT NULL, -- 'cloudinary', 'admin', 'api', 'webhook'
    triggered_by UUID, -- References auth.users(id) or users table

    -- Error handling
    error_message TEXT,
    error_code VARCHAR(100),
    retry_count INTEGER DEFAULT 0,

    -- Performance tracking
    processing_time_ms INTEGER,
    file_size BIGINT,

    -- Metadata
    operation_data JSONB, -- Store additional operation context
    webhook_data JSONB, -- Store webhook payload if applicable

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,

    -- Constraints
    CONSTRAINT valid_source CHECK (source IN ('cloudinary', 'admin', 'api', 'webhook'))
);

-- Media Usage Tracking table - Track where media is used
CREATE TABLE IF NOT EXISTS media_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Asset reference
    media_asset_id UUID NOT NULL REFERENCES media_assets(id) ON DELETE CASCADE,

    -- Usage context
    usage_type VARCHAR(100) NOT NULL, -- 'personnel_profile', 'document', 'content', 'banner'
    reference_table VARCHAR(100), -- Table name where it's used
    reference_id VARCHAR(100), -- ID in the reference table

    -- Usage metadata
    usage_context JSONB, -- Additional context data

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    removed_at TIMESTAMP WITH TIME ZONE,

    -- Constraints
    CONSTRAINT valid_usage_type CHECK (usage_type IN (
        'personnel_profile', 'document', 'content', 'banner', 'gallery', 'attachment'
    ))
);

-- Media Collections table - Organize media into collections/albums
CREATE TABLE IF NOT EXISTS media_collections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Collection details
    name VARCHAR(255) NOT NULL,
    description TEXT,
    slug VARCHAR(255) UNIQUE,

    -- Organization
    parent_collection_id UUID REFERENCES media_collections(id),
    sort_order INTEGER DEFAULT 0,

    -- Access control
    is_public BOOLEAN DEFAULT true,
    created_by UUID, -- References auth.users(id) or users table

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Media Collection Items table - Many-to-many relationship
CREATE TABLE IF NOT EXISTS media_collection_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- References
    collection_id UUID NOT NULL REFERENCES media_collections(id) ON DELETE CASCADE,
    media_asset_id UUID NOT NULL REFERENCES media_assets(id) ON DELETE CASCADE,

    -- Organization
    sort_order INTEGER DEFAULT 0,

    -- Timestamps
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    added_by UUID, -- References auth.users(id) or users table

    -- Unique constraint
    UNIQUE(collection_id, media_asset_id)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Core table indexes (with IF NOT EXISTS handling)
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_personnel_email ON personnel(email);
CREATE INDEX IF NOT EXISTS idx_personnel_department ON personnel(department);
CREATE INDEX IF NOT EXISTS idx_personnel_status ON personnel(status);
CREATE INDEX IF NOT EXISTS idx_personnel_documents_personnel_id ON personnel_documents(personnel_id);

-- Media library indexes for performance
CREATE INDEX IF NOT EXISTS idx_media_assets_cloudinary_public_id ON media_assets(cloudinary_public_id);
CREATE INDEX IF NOT EXISTS idx_media_assets_sync_status ON media_assets(sync_status);
CREATE INDEX IF NOT EXISTS idx_media_assets_resource_type ON media_assets(resource_type);
CREATE INDEX IF NOT EXISTS idx_media_assets_folder ON media_assets(folder);
CREATE INDEX IF NOT EXISTS idx_media_assets_uploaded_by ON media_assets(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_media_assets_created_at ON media_assets(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_media_assets_deleted_at ON media_assets(deleted_at);
CREATE INDEX IF NOT EXISTS idx_media_assets_tags ON media_assets USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_media_assets_mime_type ON media_assets(mime_type);
CREATE INDEX IF NOT EXISTS idx_media_assets_file_size ON media_assets(file_size);

-- Sync log indexes
CREATE INDEX IF NOT EXISTS idx_media_sync_log_media_asset_id ON media_sync_log(media_asset_id);
CREATE INDEX IF NOT EXISTS idx_media_sync_log_cloudinary_public_id ON media_sync_log(cloudinary_public_id);
CREATE INDEX IF NOT EXISTS idx_media_sync_log_operation ON media_sync_log(operation);
CREATE INDEX IF NOT EXISTS idx_media_sync_log_status ON media_sync_log(status);
CREATE INDEX IF NOT EXISTS idx_media_sync_log_created_at ON media_sync_log(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_media_sync_log_source ON media_sync_log(source);

-- Usage tracking indexes
CREATE INDEX IF NOT EXISTS idx_media_usage_media_asset_id ON media_usage(media_asset_id);
CREATE INDEX IF NOT EXISTS idx_media_usage_type ON media_usage(usage_type);
CREATE INDEX IF NOT EXISTS idx_media_usage_reference ON media_usage(reference_table, reference_id);

-- Collection indexes
CREATE INDEX IF NOT EXISTS idx_media_collections_slug ON media_collections(slug);
CREATE INDEX IF NOT EXISTS idx_media_collections_parent ON media_collections(parent_collection_id);
CREATE INDEX IF NOT EXISTS idx_media_collection_items_collection_id ON media_collection_items(collection_id);
CREATE INDEX IF NOT EXISTS idx_media_collection_items_media_asset_id ON media_collection_items(media_asset_id);

-- =====================================================
-- TRIGGERS FOR AUTO-UPDATE TIMESTAMPS
-- =====================================================

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at (with existence checks)
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_personnel_updated_at ON personnel;
CREATE TRIGGER update_personnel_updated_at BEFORE UPDATE ON personnel
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_personnel_documents_updated_at ON personnel_documents;
CREATE TRIGGER update_personnel_documents_updated_at BEFORE UPDATE ON personnel_documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Media library triggers
DROP TRIGGER IF EXISTS update_media_assets_updated_at ON media_assets;
CREATE TRIGGER update_media_assets_updated_at BEFORE UPDATE ON media_assets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_media_collections_updated_at ON media_collections;
CREATE TRIGGER update_media_collections_updated_at BEFORE UPDATE ON media_collections
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ===================================================== 
-- ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS on all tables (safe to run multiple times)
DO $$ BEGIN
    ALTER TABLE users ENABLE ROW LEVEL SECURITY;
EXCEPTION
    WHEN OTHERS THEN NULL;
END $$;

DO $$ BEGIN
    ALTER TABLE personnel ENABLE ROW LEVEL SECURITY;
EXCEPTION
    WHEN OTHERS THEN NULL;
END $$;

DO $$ BEGIN
    ALTER TABLE personnel_documents ENABLE ROW LEVEL SECURITY;
EXCEPTION
    WHEN OTHERS THEN NULL;
END $$;

-- Enable RLS on media library tables
DO $$ BEGIN
    ALTER TABLE media_assets ENABLE ROW LEVEL SECURITY;
EXCEPTION
    WHEN OTHERS THEN NULL;
END $$;

DO $$ BEGIN
    ALTER TABLE media_sync_log ENABLE ROW LEVEL SECURITY;
EXCEPTION
    WHEN OTHERS THEN NULL;
END $$;

DO $$ BEGIN
    ALTER TABLE media_usage ENABLE ROW LEVEL SECURITY;
EXCEPTION
    WHEN OTHERS THEN NULL;
END $$;

DO $$ BEGIN
    ALTER TABLE media_collections ENABLE ROW LEVEL SECURITY;
EXCEPTION
    WHEN OTHERS THEN NULL;
END $$;

DO $$ BEGIN
    ALTER TABLE media_collection_items ENABLE ROW LEVEL SECURITY;
EXCEPTION
    WHEN OTHERS THEN NULL;
END $$;

-- RLS Policies for authenticated users (with safe creation)
DROP POLICY IF EXISTS "Allow authenticated users to read users" ON users;
CREATE POLICY "Allow authenticated users to read users" ON users
    FOR SELECT USING (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Allow authenticated users to read personnel" ON personnel;
CREATE POLICY "Allow authenticated users to read personnel" ON personnel
    FOR SELECT USING (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Allow authenticated users to read personnel documents" ON personnel_documents;
CREATE POLICY "Allow authenticated users to read personnel documents" ON personnel_documents
    FOR SELECT USING (auth.role() = 'authenticated');

-- RLS Policies for service role (admin operations)
DROP POLICY IF EXISTS "Allow service role full access to users" ON users;
CREATE POLICY "Allow service role full access to users" ON users
    FOR ALL USING (auth.role() = 'service_role');

DROP POLICY IF EXISTS "Allow service role full access to personnel" ON personnel;
CREATE POLICY "Allow service role full access to personnel" ON personnel
    FOR ALL USING (auth.role() = 'service_role');

DROP POLICY IF EXISTS "Allow service role full access to personnel documents" ON personnel_documents;
CREATE POLICY "Allow service role full access to personnel documents" ON personnel_documents
    FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for media library tables

-- Media Assets policies
DROP POLICY IF EXISTS "Allow authenticated users to read media assets" ON media_assets;
CREATE POLICY "Allow authenticated users to read media assets" ON media_assets
    FOR SELECT USING (auth.role() = 'authenticated' AND deleted_at IS NULL);

DROP POLICY IF EXISTS "Allow authenticated users to insert media assets" ON media_assets;
CREATE POLICY "Allow authenticated users to insert media assets" ON media_assets
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Allow users to update their own media assets" ON media_assets;
CREATE POLICY "Allow users to update their own media assets" ON media_assets
    FOR UPDATE USING (auth.role() = 'authenticated' AND uploaded_by = auth.uid());

DROP POLICY IF EXISTS "Allow users to soft delete their own media assets" ON media_assets;
CREATE POLICY "Allow users to soft delete their own media assets" ON media_assets
    FOR UPDATE USING (auth.role() = 'authenticated' AND uploaded_by = auth.uid());

DROP POLICY IF EXISTS "Allow service role full access to media assets" ON media_assets;
CREATE POLICY "Allow service role full access to media assets" ON media_assets
    FOR ALL USING (auth.role() = 'service_role');

-- Media Sync Log policies
DROP POLICY IF EXISTS "Allow authenticated users to read sync log" ON media_sync_log;
CREATE POLICY "Allow authenticated users to read sync log" ON media_sync_log
    FOR SELECT USING (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Allow service role full access to sync log" ON media_sync_log;
CREATE POLICY "Allow service role full access to sync log" ON media_sync_log
    FOR ALL USING (auth.role() = 'service_role');

-- Media Usage policies
DROP POLICY IF EXISTS "Allow authenticated users to read media usage" ON media_usage;
CREATE POLICY "Allow authenticated users to read media usage" ON media_usage
    FOR SELECT USING (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Allow authenticated users to manage media usage" ON media_usage;
CREATE POLICY "Allow authenticated users to manage media usage" ON media_usage
    FOR ALL USING (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Allow service role full access to media usage" ON media_usage;
CREATE POLICY "Allow service role full access to media usage" ON media_usage
    FOR ALL USING (auth.role() = 'service_role');

-- Media Collections policies
DROP POLICY IF EXISTS "Allow authenticated users to read public collections" ON media_collections;
CREATE POLICY "Allow authenticated users to read public collections" ON media_collections
    FOR SELECT USING (auth.role() = 'authenticated' AND (is_public = true OR created_by = auth.uid()));

DROP POLICY IF EXISTS "Allow authenticated users to manage their collections" ON media_collections;
CREATE POLICY "Allow authenticated users to manage their collections" ON media_collections
    FOR ALL USING (auth.role() = 'authenticated' AND created_by = auth.uid());

DROP POLICY IF EXISTS "Allow service role full access to collections" ON media_collections;
CREATE POLICY "Allow service role full access to collections" ON media_collections
    FOR ALL USING (auth.role() = 'service_role');

-- Media Collection Items policies
DROP POLICY IF EXISTS "Allow authenticated users to read collection items" ON media_collection_items;
CREATE POLICY "Allow authenticated users to read collection items" ON media_collection_items
    FOR SELECT USING (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Allow authenticated users to manage collection items" ON media_collection_items;
CREATE POLICY "Allow authenticated users to manage collection items" ON media_collection_items
    FOR ALL USING (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Allow service role full access to collection items" ON media_collection_items;
CREATE POLICY "Allow service role full access to collection items" ON media_collection_items
    FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- PRODUCTION READY - NO SAMPLE DATA
-- =====================================================

-- Database is ready for production use
-- All tables, indexes, functions, and security policies are configured
-- Ready to accept real data through the application interface

-- =====================================================
-- ENTERPRISE MEDIA LIBRARY FUNCTIONS
-- =====================================================

-- Function to soft delete media asset by Cloudinary public ID
CREATE OR REPLACE FUNCTION soft_delete_media_asset(asset_id TEXT, deleted_by_user TEXT DEFAULT NULL)
RETURNS BOOLEAN AS $$
DECLARE
    asset_uuid UUID;
    deleted_by_uuid UUID;
BEGIN
    -- Convert string parameters to UUIDs where needed
    IF deleted_by_user IS NOT NULL AND deleted_by_user != '' THEN
        deleted_by_uuid := deleted_by_user::UUID;
    END IF;

    -- Update the media asset using cloudinary_public_id
    UPDATE media_assets
    SET
        deleted_at = NOW(),
        deleted_by = deleted_by_uuid,
        sync_status = 'pending'
    WHERE cloudinary_public_id = asset_id AND deleted_at IS NULL;

    -- Get the asset UUID for logging
    SELECT id INTO asset_uuid FROM media_assets WHERE cloudinary_public_id = asset_id;

    -- Log the operation
    INSERT INTO media_sync_log (
        operation, status, media_asset_id, cloudinary_public_id,
        source, triggered_by, operation_data
    )
    VALUES (
        'delete', 'pending', asset_uuid, asset_id,
        'admin', deleted_by_uuid,
        jsonb_build_object('soft_delete', true, 'deleted_at', NOW())
    );

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to restore soft deleted media asset by Cloudinary public ID
CREATE OR REPLACE FUNCTION restore_media_asset(asset_id TEXT, restored_by_user TEXT DEFAULT NULL)
RETURNS BOOLEAN AS $$
DECLARE
    asset_uuid UUID;
    restored_by_uuid UUID;
BEGIN
    -- Convert string parameters to UUIDs where needed
    IF restored_by_user IS NOT NULL AND restored_by_user != '' THEN
        restored_by_uuid := restored_by_user::UUID;
    END IF;

    -- Update the media asset using cloudinary_public_id
    UPDATE media_assets
    SET
        deleted_at = NULL,
        deleted_by = NULL,
        sync_status = 'pending'
    WHERE cloudinary_public_id = asset_id AND deleted_at IS NOT NULL;

    -- Get the asset UUID for logging
    SELECT id INTO asset_uuid FROM media_assets WHERE cloudinary_public_id = asset_id;

    -- Log the operation
    INSERT INTO media_sync_log (
        operation, status, media_asset_id, cloudinary_public_id,
        source, triggered_by, operation_data
    )
    VALUES (
        'restore', 'pending', asset_uuid, asset_id,
        'admin', restored_by_uuid,
        jsonb_build_object('restored_at', NOW())
    );

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update sync status by Cloudinary public ID
CREATE OR REPLACE FUNCTION update_media_sync_status(
    asset_id TEXT,
    new_status media_sync_status,
    error_msg TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Update the media asset using cloudinary_public_id
    UPDATE media_assets
    SET
        sync_status = new_status,
        last_synced_at = CASE WHEN new_status = 'synced' THEN NOW() ELSE last_synced_at END,
        sync_error_message = error_msg,
        sync_retry_count = CASE WHEN new_status = 'error' THEN sync_retry_count + 1 ELSE 0 END
    WHERE cloudinary_public_id = asset_id;

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get media statistics
CREATE OR REPLACE FUNCTION get_media_statistics()
RETURNS TABLE (
    total_assets BIGINT,
    total_images BIGINT,
    total_videos BIGINT,
    total_raw BIGINT,
    total_size BIGINT,
    synced_assets BIGINT,
    pending_assets BIGINT,
    error_assets BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*) as total_assets,
        COUNT(*) FILTER (WHERE resource_type = 'image') as total_images,
        COUNT(*) FILTER (WHERE resource_type = 'video') as total_videos,
        COUNT(*) FILTER (WHERE resource_type = 'raw') as total_raw,
        COALESCE(SUM(file_size), 0)::BIGINT as total_size,
        COUNT(*) FILTER (WHERE sync_status = 'synced') as synced_assets,
        COUNT(*) FILTER (WHERE sync_status = 'pending') as pending_assets,
        COUNT(*) FILTER (WHERE sync_status = 'error') as error_assets
    FROM media_assets
    WHERE deleted_at IS NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up old sync logs (keep last 30 days)
CREATE OR REPLACE FUNCTION cleanup_old_sync_logs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM media_sync_log
    WHERE created_at < NOW() - INTERVAL '30 days';

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- DOCUMENTATION COMMENTS
-- =====================================================

COMMENT ON TABLE users IS 'System users with authentication credentials';
COMMENT ON TABLE personnel IS 'Personnel/staff information and profiles';
COMMENT ON TABLE personnel_documents IS 'Documents associated with personnel records';

-- Media library table comments
COMMENT ON TABLE media_assets IS 'Core media assets table with bidirectional Cloudinary sync';
COMMENT ON TABLE media_sync_log IS 'Audit trail for all media synchronization operations';
COMMENT ON TABLE media_usage IS 'Track where and how media assets are used across the system';
COMMENT ON TABLE media_collections IS 'Organize media assets into collections/albums';
COMMENT ON TABLE media_collection_items IS 'Many-to-many relationship between collections and assets';

COMMENT ON COLUMN users.role IS 'User role (admin, user, etc.)';
COMMENT ON COLUMN users.status IS 'Account status (ACTIVE, INACTIVE, SUSPENDED)';
COMMENT ON COLUMN personnel.status IS 'Employment status (Active, Inactive, On Leave, Suspended)';
COMMENT ON COLUMN personnel.hire_date IS 'Date when the personnel was hired';
COMMENT ON COLUMN personnel_documents.size IS 'File size in bytes';
COMMENT ON COLUMN personnel_documents.path IS 'Storage path or URL to the document';

-- Media library column comments
COMMENT ON COLUMN media_assets.cloudinary_public_id IS 'Unique identifier in Cloudinary (required for sync)';
COMMENT ON COLUMN media_assets.cloudinary_version IS 'Version number from Cloudinary for change tracking';
COMMENT ON COLUMN media_assets.cloudinary_signature IS 'Cloudinary signature for integrity verification';
COMMENT ON COLUMN media_assets.sync_status IS 'Current synchronization status with Cloudinary';
COMMENT ON COLUMN media_assets.deleted_at IS 'Soft delete timestamp - NULL means active';
COMMENT ON COLUMN media_assets.tags IS 'Array of tags for categorization and search';
COMMENT ON COLUMN media_assets.secure_url IS 'HTTPS URL for secure access to the asset';
COMMENT ON COLUMN media_assets.resource_type IS 'Type of resource: image, video, or raw file';
COMMENT ON COLUMN media_sync_log.operation IS 'Type of sync operation performed';
COMMENT ON COLUMN media_sync_log.source IS 'Source that triggered the sync operation';
COMMENT ON COLUMN media_usage.usage_type IS 'How the media asset is being used in the system';
COMMENT ON COLUMN media_collections.is_public IS 'Whether the collection is publicly accessible';

-- =====================================================
-- PERMISSIONS
-- =====================================================

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Grant execute permissions on media library functions
GRANT EXECUTE ON FUNCTION soft_delete_media_asset(TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION restore_media_asset(TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION update_media_sync_status(TEXT, media_sync_status, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_media_statistics() TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_old_sync_logs() TO authenticated;

-- =====================================================
-- PRODUCTION MEDIA LIBRARY SETUP
-- =====================================================

-- Media library is ready for production use
-- Collections, assets, and sync functionality will be managed through the application
-- All tables are configured with proper constraints, indexes, and security policies

-- =====================================================
-- MAINTENANCE PROCEDURES
-- =====================================================

-- Create a scheduled function to clean up old sync logs (run daily)
-- This should be set up as a cron job or scheduled function in your deployment

-- Example usage of maintenance functions:
-- SELECT cleanup_old_sync_logs(); -- Clean up logs older than 30 days
-- SELECT * FROM get_media_statistics(); -- Get current media statistics

-- =====================================================
-- SETUP VERIFICATION
-- =====================================================

-- Run these queries to verify the setup was successful:

-- 1. Check if all tables exist
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('users', 'personnel', 'personnel_documents', 'media_assets', 'media_sync_log', 'media_usage', 'media_collections', 'media_collection_items')
ORDER BY table_name;

-- 2. Check if all functions exist
SELECT routine_name
FROM information_schema.routines
WHERE routine_schema = 'public'
AND routine_name IN ('soft_delete_media_asset', 'restore_media_asset', 'update_media_sync_status', 'get_media_statistics', 'cleanup_old_sync_logs')
ORDER BY routine_name;

-- 3. Test the media statistics function
SELECT * FROM get_media_statistics();

-- 4. Check if all indexes exist
SELECT indexname
FROM pg_indexes
WHERE schemaname = 'public'
AND indexname LIKE 'idx_media_%'
ORDER BY indexname;

-- =====================================================
-- POST-SETUP INSTRUCTIONS
-- =====================================================

-- After running this script successfully:
-- 1. Your database schema is ready for production use
-- 2. The media library sync functionality should work without "Failed to fetch" errors
-- 3. All media operations (upload, delete, sync) will be properly tracked
-- 4. You can now test the sync button in your admin panel
-- 5. Media assets will be bidirectionally synced between Cloudinary and Supabase

-- If you encounter any issues:
-- 1. Check the verification queries above
-- 2. Ensure all functions return results
-- 3. Verify your environment variables are set correctly
-- 4. Test the /api/setup-media-db endpoint to confirm setup

-- =====================================================
-- SCRIPT COMPLETE - DATABASE READY FOR PRODUCTION
-- =====================================================