<!DOCTYPE html>
<html>
<head>
    <title>Test Media Upload</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin: 20px 0; }
        .result { margin: 20px 0; padding: 15px; background: #f5f5f5; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Media Library Test</h1>
        <p>Test file upload and database persistence</p>
        
        <div class="upload-area">
            <input type="file" id="fileInput" accept="image/*,video/*" />
            <br><br>
            <button onclick="uploadFile()">Upload Test File</button>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
        
        <div>
            <h3>Quick Tests:</h3>
            <button onclick="testDatabase()">Test Database</button>
            <button onclick="testSync()">Test Sync</button>
            <button onclick="loadMedia()">Load Media</button>
        </div>
        
        <div id="mediaList" style="margin-top: 20px;"></div>
    </div>

    <script>
        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('Please select a file first', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', file);
            formData.append('folder', 'test-uploads');
            formData.append('description', 'Test upload from HTML');
            
            try {
                showResult('Uploading...', 'info');
                
                const response = await fetch('/api/cloudinary/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult(`✅ Upload successful!
                    Public ID: ${result.data.public_id}
                    URL: ${result.data.secure_url}
                    Database Sync: ${result.database_sync?.success ? '✅ Success' : '❌ Failed'}
                    ${result.warnings?.length ? 'Warnings: ' + result.warnings.join(', ') : ''}`, 'success');
                } else {
                    showResult(`❌ Upload failed: ${result.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Upload error: ${error.message}`, 'error');
            }
        }
        
        async function testDatabase() {
            try {
                showResult('Testing database...', 'info');
                
                const response = await fetch('/api/setup-media-db');
                const result = await response.json();
                
                if (result.success) {
                    showResult('✅ Database is properly configured!', 'success');
                } else {
                    showResult(`⚠️ Database issues: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Database test failed: ${error.message}`, 'error');
            }
        }
        
        async function testSync() {
            try {
                showResult('Testing sync...', 'info');
                
                const response = await fetch('/api/cloudinary/sync', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult(`✅ Sync successful! Synced: ${result.data.synced_items}, Updated: ${result.data.updated_items}`, 'success');
                } else {
                    showResult(`⚠️ Sync completed with issues: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Sync test failed: ${error.message}`, 'error');
            }
        }
        
        async function loadMedia() {
            try {
                showResult('Loading media...', 'info');
                
                const response = await fetch('/api/cloudinary/media');
                const result = await response.json();
                
                const mediaList = document.getElementById('mediaList');
                
                if (result.success && result.items.length > 0) {
                    showResult(`✅ Found ${result.items.length} media items`, 'success');
                    
                    mediaList.innerHTML = '<h3>Media Items:</h3>' + 
                        result.items.map(item => `
                            <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px;">
                                <strong>${item.original_filename || item.cloudinary_public_id}</strong><br>
                                <small>Format: ${item.format}, Size: ${(item.file_size / 1024).toFixed(1)} KB</small><br>
                                <img src="${item.secure_url}" style="max-width: 200px; max-height: 150px;" />
                            </div>
                        `).join('');
                } else {
                    showResult(`📭 No media items found. Database ready: ${result.database_setup?.ready}`, 'info');
                    mediaList.innerHTML = '';
                }
            } catch (error) {
                showResult(`❌ Load media failed: ${error.message}`, 'error');
            }
        }
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = message.replace(/\n/g, '<br>');
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
        
        // Auto-test on load
        window.onload = function() {
            testDatabase();
        };
    </script>
</body>
</html>
