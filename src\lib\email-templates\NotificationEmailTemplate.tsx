/**
 * Notification Email Template
 * 
 * Professional notification email template for system notifications
 * Uses React components for rich HTML email content
 */

import * as React from 'react'

interface NotificationEmailTemplateProps {
  recipientName: string
  subject: string
  message: string
  actionUrl?: string
  actionText?: string
}

export const NotificationEmailTemplate: React.FC<Readonly<NotificationEmailTemplateProps>> = ({
  recipientName,
  subject,
  message,
  actionUrl,
  actionText,
}) => (
  <div style={{
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    maxWidth: '600px',
    margin: '0 auto',
    padding: '20px',
    backgroundColor: '#ffffff'
  }}>
    {/* Header */}
    <div style={{
      textAlign: 'center',
      marginBottom: '32px',
      paddingBottom: '20px',
      borderBottom: '2px solid #e5e7eb'
    }}>
      <h1 style={{
        color: '#1f2937',
        fontSize: '24px',
        fontWeight: 'bold',
        margin: '0',
        marginBottom: '8px'
      }}>
        LGU Project Notification
      </h1>
      <p style={{
        color: '#6b7280',
        fontSize: '14px',
        margin: '0'
      }}>
        System Notification
      </p>
    </div>

    {/* Main Content */}
    <div style={{ marginBottom: '32px' }}>
      <h2 style={{
        color: '#1f2937',
        fontSize: '18px',
        fontWeight: '600',
        marginBottom: '8px'
      }}>
        Hello {recipientName},
      </h2>
      
      <h3 style={{
        color: '#1f2937',
        fontSize: '20px',
        fontWeight: '600',
        marginBottom: '16px',
        marginTop: '20px'
      }}>
        {subject}
      </h3>
      
      <div style={{
        backgroundColor: '#f9fafb',
        border: '1px solid #e5e7eb',
        borderRadius: '8px',
        padding: '20px',
        marginBottom: '24px'
      }}>
        <p style={{
          color: '#374151',
          fontSize: '16px',
          lineHeight: '1.6',
          margin: '0',
          whiteSpace: 'pre-wrap'
        }}>
          {message}
        </p>
      </div>

      {/* Call to Action Button (if provided) */}
      {actionUrl && actionText && (
        <div style={{ textAlign: 'center', marginBottom: '24px' }}>
          <a
            href={actionUrl}
            style={{
              display: 'inline-block',
              backgroundColor: '#3b82f6',
              color: '#ffffff',
              textDecoration: 'none',
              padding: '12px 24px',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '600',
              border: 'none',
              cursor: 'pointer'
            }}
          >
            {actionText}
          </a>
        </div>
      )}

      <p style={{
        color: '#6b7280',
        fontSize: '14px',
        lineHeight: '1.6',
        marginTop: '24px'
      }}>
        This notification was automatically generated by the LGU Project system. 
        If you have any questions, please contact your system administrator.
      </p>
    </div>

    {/* Info Box */}
    <div style={{
      backgroundColor: '#eff6ff',
      border: '1px solid #3b82f6',
      borderRadius: '6px',
      padding: '16px',
      marginBottom: '32px'
    }}>
      <h4 style={{
        color: '#1e40af',
        fontSize: '14px',
        fontWeight: '600',
        margin: '0',
        marginBottom: '8px'
      }}>
        ℹ️ Need Help?
      </h4>
      <p style={{
        color: '#1e40af',
        fontSize: '13px',
        lineHeight: '1.5',
        margin: '0'
      }}>
        If you need assistance or have questions about this notification, 
        please contact your system administrator or visit the help section in your dashboard.
      </p>
    </div>

    {/* Footer */}
    <div style={{
      textAlign: 'center',
      paddingTop: '20px',
      borderTop: '1px solid #e5e7eb',
      color: '#6b7280',
      fontSize: '12px'
    }}>
      <p style={{ margin: '0', marginBottom: '8px' }}>
        This email was sent by LGU Project Admin System
      </p>
      <p style={{ margin: '0', marginBottom: '8px' }}>
        Automated System Notification
      </p>
      <p style={{ margin: '0' }}>
        © 2024 LGU Project. All rights reserved.
      </p>
    </div>
  </div>
)
