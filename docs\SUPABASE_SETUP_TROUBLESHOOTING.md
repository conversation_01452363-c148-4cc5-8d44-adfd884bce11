# 🚨 SUPABASE SETUP TROUBLESHOOTING GUIDE

## **CRITICAL ISSUE IDENTIFIED**

Your database diagnostic shows that **ALL tables are missing**, including basic tables like `users`, `personnel`, etc. This indicates the SQL script either:

- ❌ **Failed to execute** in Supabase SQL Editor
- ❌ **Had errors** that weren't visible
- ❌ **Was run in wrong database**
- ❌ **Had permission issues**

---

## **IMMEDIATE SOLUTION STEPS**

### **Step 1: Verify Supabase Project**
1. **Open your Supabase dashboard**
2. **Confirm you're in the correct project**
3. **Check the project URL matches your environment variable**

### **Step 2: Check SQL Editor History**
1. **Go to SQL Editor** in Supabase dashboard
2. **Check "History" tab** to see if the script was actually executed
3. **Look for any error messages** in the execution results

### **Step 3: Run Script in Smaller Chunks**

Instead of running the entire 771-line script at once, run it in these chunks:

#### **Chunk 1: Basic Setup (Lines 1-100)**
```sql
-- Create custom types/enums (with IF NOT EXISTS handling)
DO $$ BEGIN
    CREATE TYPE user_status AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE personnel_status AS ENUM ('Active', 'Inactive', 'On Leave', 'Suspended');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE media_sync_status AS ENUM ('synced', 'pending', 'error');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE media_sync_operation AS ENUM ('upload', 'delete', 'update', 'restore');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id BIGSERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    role VARCHAR(50) DEFAULT 'user',
    status user_status DEFAULT 'ACTIVE',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Run this first and verify it works before proceeding.**

#### **Chunk 2: Personnel Tables (Lines 101-200)**
```sql
-- Personnel table
CREATE TABLE IF NOT EXISTS personnel (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    profile_photo VARCHAR(500),
    department VARCHAR(255) NOT NULL,
    position VARCHAR(255),
    hire_date DATE,
    status personnel_status DEFAULT 'Active',
    biography TEXT,
    spouse_name VARCHAR(255),
    spouse_occupation VARCHAR(255),
    children_count VARCHAR(10),
    emergency_contact VARCHAR(50),
    children_names TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Personnel Documents table
CREATE TABLE IF NOT EXISTS personnel_documents (
    id BIGSERIAL PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    size INTEGER NOT NULL,
    path VARCHAR(500) NOT NULL,
    personnel_id BIGINT NOT NULL REFERENCES personnel(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **Chunk 3: Media Assets Table (Lines 201-300)**
```sql
-- Media Assets table - Core table for bidirectional Cloudinary sync
CREATE TABLE IF NOT EXISTS media_assets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Cloudinary identifiers (required for sync)
    cloudinary_public_id VARCHAR(500) NOT NULL UNIQUE,
    cloudinary_version INTEGER NOT NULL DEFAULT 1,
    cloudinary_signature VARCHAR(255) NOT NULL,
    cloudinary_etag VARCHAR(255),

    -- File information
    original_filename VARCHAR(500),
    display_name VARCHAR(500),
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    format VARCHAR(50) NOT NULL,
    width INTEGER,
    height INTEGER,
    duration DECIMAL(10,2),

    -- Organization and categorization
    folder VARCHAR(500),
    tags TEXT[] DEFAULT '{}',
    description TEXT,
    alt_text VARCHAR(500),

    -- URLs (cached for performance)
    secure_url VARCHAR(1000) NOT NULL,
    url VARCHAR(1000) NOT NULL,
    thumbnail_url VARCHAR(1000),

    -- Resource type and access
    resource_type VARCHAR(50) NOT NULL DEFAULT 'image',
    access_mode VARCHAR(50) DEFAULT 'public',

    -- User and business context
    uploaded_by UUID,
    used_in_personnel BIGINT REFERENCES personnel(id),
    used_in_documents BIGINT REFERENCES personnel_documents(id),

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    cloudinary_created_at TIMESTAMP WITH TIME ZONE,

    -- Sync management
    sync_status media_sync_status DEFAULT 'synced',
    last_synced_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sync_error_message TEXT,
    sync_retry_count INTEGER DEFAULT 0,

    -- Soft delete for data integrity
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID,

    -- Constraints
    CONSTRAINT valid_resource_type CHECK (resource_type IN ('image', 'video', 'raw')),
    CONSTRAINT valid_access_mode CHECK (access_mode IN ('public', 'authenticated')),
    CONSTRAINT positive_file_size CHECK (file_size > 0),
    CONSTRAINT positive_dimensions CHECK (
        (width IS NULL OR width > 0) AND
        (height IS NULL OR height > 0)
    )
);
```

### **Step 4: Verify Each Chunk**

After running each chunk, verify it worked:

```sql
-- Check if tables were created
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;
```

### **Step 5: Continue with Remaining Chunks**

Only proceed to the next chunk if the previous one succeeded.

---

## **COMMON ISSUES & SOLUTIONS**

### **Issue 1: Permission Denied**
- **Solution**: Make sure you're using the **service role key** in your environment
- **Check**: Your `.env.local` has `SUPABASE_SERVICE_ROLE_KEY` not `SUPABASE_ANON_KEY`

### **Issue 2: Wrong Database**
- **Solution**: Verify the project URL in Supabase dashboard matches your `.env.local`

### **Issue 3: Script Timeout**
- **Solution**: Run the script in smaller chunks as shown above

### **Issue 4: Syntax Errors**
- **Solution**: Copy-paste exactly from the file, don't modify anything

---

## **VERIFICATION COMMANDS**

Run these in Supabase SQL Editor to verify setup:

```sql
-- 1. Check all tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'personnel', 'personnel_documents', 'media_assets', 'media_sync_log')
ORDER BY table_name;

-- 2. Check functions exist  
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_schema = 'public'
AND routine_name LIKE '%media%'
ORDER BY routine_name;

-- 3. Test media_assets table
SELECT COUNT(*) FROM media_assets;

-- 4. Test statistics function
SELECT * FROM get_media_statistics();
```

---

## **NEXT STEPS**

1. **Run Chunk 1** and verify it works
2. **Continue with remaining chunks** one by one
3. **Test the verification commands** after each chunk
4. **Check the debug endpoint**: `GET /api/debug-database`
5. **Refresh your media library** page

The issue is definitely in the database setup, not the application code. Once the tables are properly created, your media library will work perfectly with full bidirectional sync.
