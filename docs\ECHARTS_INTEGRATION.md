# ECharts Integration Guide

## 🚀 **Professional ECharts Implementation**

This guide covers the complete integration of ECharts into the LGU Project App with both direct ECharts implementation and React wrapper approaches.

---

## 📦 **Installed Dependencies**

```json
{
  "dependencies": {
    "echarts": "^5.4.3",
    "echarts-for-react": "^3.0.2"
  },
  "devDependencies": {
    "@types/echarts": "^4.9.19"
  }
}
```

---

## 🏗️ **Project Structure**

```
src/
├── app/admin/charts/
│   └── page.tsx                    # Main charts showcase page
├── components/charts/
│   ├── index.ts                    # Export barrel file
│   ├── ChartWrapper.tsx            # Direct ECharts wrapper
│   ├── LineChart.tsx               # Line chart component
│   ├── BarChart.tsx                # Bar chart component
│   └── PieChart.tsx                # Pie chart component
└── docs/
    └── ECHARTS_INTEGRATION.md      # This documentation
```

---

## 🎯 **Implementation Approaches**

### **1. Direct ECharts Implementation**

Best for maximum control and performance. Our implementation includes multiple professional examples:

#### **Advanced Line Chart with Area Fill**
```typescript
import * as echarts from 'echarts'
import { useEffect, useRef } from 'react'

const DirectLineChart = () => {
  const chartRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!chartRef.current) return

    const chart = echarts.init(chartRef.current)

    const option = {
      title: {
        text: 'Direct ECharts - Advanced Line Chart',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
          color: '#374151'
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e5e7eb',
        borderWidth: 1
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['Q1', 'Q2', 'Q3', 'Q4'],
        axisLine: { lineStyle: { color: '#e5e7eb' } }
      },
      yAxis: {
        type: 'value',
        axisLine: { lineStyle: { color: '#e5e7eb' } },
        splitLine: { lineStyle: { color: '#f3f4f6' } }
      },
      series: [{
        name: 'Sales',
        type: 'line',
        data: [120, 132, 101, 134],
        smooth: true,
        lineStyle: { color: '#3b82f6', width: 3 },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
              { offset: 1, color: 'rgba(59, 130, 246, 0.1)' }
            ]
          }
        },
        emphasis: { focus: 'series' }
      }]
    }

    chart.setOption(option)

    // Handle resize
    const handleResize = () => chart.resize()
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chart.dispose()
    }
  }, [])

  return <div ref={chartRef} style={{ width: '100%', height: '400px' }} />
}
```

#### **Professional 3D Bar Chart**
```typescript
const Direct3DBarChart = () => {
  const chartRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!chartRef.current) return

    const chart = echarts.init(chartRef.current)

    const option = {
      title: {
        text: 'Direct ECharts - 3D Bar Chart',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' }
      },
      series: [{
        name: 'Revenue',
        type: 'bar',
        data: [120, 200, 150, 80, 70, 110],
        itemStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: '#3b82f6' },
              { offset: 1, color: '#1d4ed8' }
            ]
          },
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }

    chart.setOption(option)
    // ... resize handling
  }, [])

  return <div ref={chartRef} style={{ width: '100%', height: '400px' }} />
}
```

#### **Advanced Multi-Series Scatter Plot**
```typescript
const DirectScatterChart = () => {
  const chartRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!chartRef.current) return

    const chart = echarts.init(chartRef.current)

    // Generate sophisticated scatter data
    const scatterData1 = Array.from({ length: 50 }, () => [
      Math.random() * 100,
      Math.random() * 100,
      Math.random() * 50 + 10
    ])

    const option = {
      title: {
        text: 'Direct ECharts - Multi-Series Scatter Plot',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: function(params) {
          return `${params.seriesName}<br/>X: ${params.data[0].toFixed(2)}<br/>Y: ${params.data[1].toFixed(2)}<br/>Size: ${params.data[2].toFixed(2)}`
        }
      },
      xAxis: {
        name: 'Performance Score',
        type: 'value',
        splitLine: { lineStyle: { type: 'dashed' } }
      },
      yAxis: {
        name: 'Efficiency Rating',
        type: 'value',
        splitLine: { lineStyle: { type: 'dashed' } }
      },
      series: [{
        name: 'Dataset A',
        type: 'scatter',
        data: scatterData1,
        symbolSize: function(data) {
          return Math.sqrt(data[2]) * 2
        },
        itemStyle: {
          color: '#3b82f6',
          opacity: 0.7
        },
        emphasis: {
          itemStyle: {
            borderColor: '#1d4ed8',
            borderWidth: 2
          }
        }
      }]
    }

    chart.setOption(option)
    // ... resize handling
  }, [])

  return <div ref={chartRef} style={{ width: '100%', height: '500px' }} />
}
```

### **2. React ECharts Wrapper**

Best for rapid development and React integration:

```typescript
import ReactECharts from 'echarts-for-react'

const ReactChart = () => {
  const option = {
    title: { text: 'My Chart' },
    xAxis: { type: 'category', data: ['A', 'B', 'C'] },
    yAxis: { type: 'value' },
    series: [{ type: 'bar', data: [10, 20, 30] }]
  }

  return (
    <ReactECharts
      option={option}
      style={{ height: '400px' }}
      opts={{ renderer: 'canvas' }}
    />
  )
}
```

---

## 🎨 **Available Chart Types**

### **Line Charts**
- Basic line charts with smooth curves
- Area charts with gradient fills
- Multi-series line charts
- Time series data visualization
- Professional styling with emphasis effects

### **Bar Charts**
- Vertical bars with 3D gradient effects
- Horizontal bars
- Stacked bars
- Grouped bars
- Professional border radius and shadows

### **Pie Charts**
- Standard pie charts
- Donut charts with custom radius
- Multiple pie charts
- Custom legends with positioning options

### **Scatter Plots**
- Multi-series scatter plots
- Variable symbol sizes based on data
- Professional tooltips with detailed information
- Emphasis effects with borders

### **Radar Charts**
- Multi-dimensional data comparison
- Performance vs target visualization
- Custom indicators and scales

### **Gauge Charts**
- Single gauge with color zones
- Multi-gauge dashboards
- CPU/Memory monitoring style
- Custom formatting and colors

### **Heatmaps**
- Weekly activity heatmaps
- Professional color gradients
- Interactive tooltips
- Time-based data visualization

### **Candlestick Charts**
- Financial data visualization
- OHLC (Open, High, Low, Close) format
- Professional color coding (green/red)
- Data zoom functionality
- Cross-axis pointer

### **Funnel Charts**
- Sales funnel analysis
- Conversion rate visualization
- Professional styling with gaps
- Interactive emphasis effects

### **Tree Charts**
- Organization structure visualization
- Hierarchical data representation
- Expandable/collapsible nodes
- Professional node styling

### **Advanced Combination Charts**
- Bar + Line combinations
- Dual Y-axis support
- Cross-axis pointers
- Professional legends and tooltips

---

## 🛠️ **Reusable Components**

### **ChartWrapper**
Direct ECharts wrapper with loading states and error handling:

```typescript
import { ChartWrapper } from '@/components/charts'

<ChartWrapper
  option={chartOption}
  height="500px"
  loading={isLoading}
  onChartReady={(chart) => console.log('Chart ready:', chart)}
/>
```

### **LineChart**
Simplified line chart component:

```typescript
import { LineChart } from '@/components/charts'

<LineChart
  title="Sales Trend"
  xAxisData={['Jan', 'Feb', 'Mar']}
  series={[
    { name: 'Sales', data: [100, 200, 150], color: '#3b82f6' }
  ]}
  smooth={true}
  showArea={true}
/>
```

### **BarChart**
Flexible bar chart component:

```typescript
import { BarChart } from '@/components/charts'

<BarChart
  title="Revenue by Month"
  xAxisData={['Q1', 'Q2', 'Q3', 'Q4']}
  series={[
    { name: 'Revenue', data: [120, 200, 150, 80] },
    { name: 'Profit', data: [80, 130, 90, 50] }
  ]}
  stacked={false}
  horizontal={false}
/>
```

### **PieChart**
Customizable pie chart component:

```typescript
import { PieChart } from '@/components/charts'

<PieChart
  title="Market Share"
  data={[
    { name: 'Desktop', value: 1048 },
    { name: 'Mobile', value: 735 },
    { name: 'Tablet', value: 580 }
  ]}
  donut={true}
  showLegend={true}
  legendPosition="bottom"
/>
```

---

## 🎨 **Theming and Styling**

### **Color Palettes**

```typescript
import { chartColorPalettes } from '@/components/charts'

// Available palettes
chartColorPalettes.default     // Blue, green, yellow, red, purple
chartColorPalettes.professional // Grayscale professional
chartColorPalettes.vibrant     // Bright, energetic colors
chartColorPalettes.pastel      // Soft, muted colors
chartColorPalettes.ocean       // Blue ocean theme
```

### **Custom Themes**

```typescript
const customTheme = {
  primaryColor: '#your-color',
  secondaryColor: '#your-secondary',
  backgroundColor: '#ffffff',
  textColor: '#374151',
  gridColor: '#f3f4f6'
}
```

---

## 📱 **Responsive Design**

All charts are responsive by default:

```typescript
// Automatic resize handling
useEffect(() => {
  const handleResize = () => chart.resize()
  window.addEventListener('resize', handleResize)
  return () => window.removeEventListener('resize', handleResize)
}, [chart])
```

---

## 🚀 **Performance Optimization**

### **Canvas vs SVG Rendering**
```typescript
// Canvas (better for large datasets)
<ReactECharts opts={{ renderer: 'canvas' }} />

// SVG (better for small datasets, scalable)
<ReactECharts opts={{ renderer: 'svg' }} />
```

### **Data Sampling**
```typescript
// For large datasets
const option = {
  series: [{
    type: 'line',
    data: largeDataset,
    sampling: 'lttb' // Largest-Triangle-Three-Buckets
  }]
}
```

---

## 🔧 **Best Practices**

1. **Always dispose charts** when components unmount
2. **Handle resize events** for responsive behavior
3. **Use loading states** for better UX
4. **Optimize data structures** for large datasets
5. **Choose appropriate renderers** based on data size
6. **Implement error boundaries** for chart failures
7. **Use TypeScript interfaces** for type safety

---

## 📊 **Example Usage**

Visit `/admin/charts` to see the complete showcase with:
- All chart types demonstrated
- Both implementation approaches
- Interactive examples
- Professional styling
- Responsive design
- Loading states
- Error handling

---

## 🔗 **Resources**

- [ECharts Official Documentation](https://echarts.apache.org/en/index.html)
- [ECharts for React](https://github.com/hustcc/echarts-for-react)
- [ECharts Examples Gallery](https://echarts.apache.org/examples/en/index.html)
- [TypeScript Support](https://echarts.apache.org/en/tutorial.html#ECharts%20TypeScript%20Interface)
